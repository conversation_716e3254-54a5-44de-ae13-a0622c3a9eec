/**
 * Componente HorizontalTabBar
 * Barra de navegación horizontal con pestañas para la aplicación Hydra21
 * Incluye las pestañas: Inicio, IA, Carpetas, Herramientas
 */

import React, { useState } from 'react';
import {
  Home,
  Brain,
  FolderOpen,
  Wrench,
  ChevronRight
} from 'lucide-react';
import { useTheme } from '../../hooks/useTheme';

// Definición de tipos para las pestañas
export interface TabItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  content?: React.ReactNode;
  disabled?: boolean;
}

interface HorizontalTabBarProps {
  className?: string;
  onTabChange?: (tabId: string) => void;
  defaultTab?: string;
}

// Configuración de las pestañas principales
const defaultTabs: TabItem[] = [
  {
    id: 'inicio',
    label: 'Inicio',
    icon: <Home size={16} />,
  },
  {
    id: 'ia',
    label: 'IA',
    icon: <Brain size={16} />,
  },
  {
    id: 'carpetas',
    label: 'Carpetas',
    icon: <FolderOpen size={16} />,
  },
  {
    id: 'herramientas',
    label: 'Herramientas',
    icon: <Wrench size={16} />,
  },
];

export const HorizontalTabBar: React.FC<HorizontalTabBarProps> = ({
  className = '',
  onTabChange,
  defaultTab = 'inicio'
}) => {
  const { isDark } = useTheme();
  const [activeTab, setActiveTab] = useState(defaultTab);

  const handleTabClick = (tabId: string) => {
    if (activeTab !== tabId) {
      setActiveTab(tabId);
      onTabChange?.(tabId);
    }
  };

  // Estilos base para la barra de pestañas con glassmorphism
  const tabBarClasses = `
    flex items-center border-b backdrop-blur-md transition-all duration-300
    ${isDark
      ? 'glass-effect-dark border-white/10'
      : 'glass-effect border-black/10'
    }
    ${className}
  `.trim();

  // Estilos para cada pestaña
  const getTabClasses = (tabId: string, disabled?: boolean) => {
    const isActive = activeTab === tabId;

    if (disabled) {
      return `
        flex items-center gap-2 px-4 py-3 text-sm font-medium
        opacity-50 cursor-not-allowed
        ${isDark ? 'text-gray-500' : 'text-gray-400'}
      `.trim();
    }

    return `
      flex items-center gap-2 px-4 py-3 text-sm font-medium
      transition-all duration-300 ease-in-out cursor-pointer
      relative border-b-2 hover:scale-105 rounded-t-lg
      ${isActive
        ? `border-blue-500 ${isDark
            ? 'text-blue-400 glass-gradient-dark'
            : 'text-blue-600 glass-gradient'
          }`
        : `border-transparent hover:border-gray-300 ${isDark
            ? 'text-gray-300 hover:text-gray-100 glass-hover-dark'
            : 'text-gray-600 hover:text-gray-900 glass-hover'
          }`
      }
    `.trim();
  };

  return (
    <div className={tabBarClasses}>
      {/* Contenedor de pestañas */}
      <div className="flex items-center">
        {defaultTabs.map((tab) => (
          <button
            key={tab.id}
            className={getTabClasses(tab.id, tab.disabled)}
            onClick={() => !tab.disabled && handleTabClick(tab.id)}
            disabled={tab.disabled}
            role="tab"
            aria-selected={activeTab === tab.id}
            aria-controls={`tabpanel-${tab.id}`}
          >
            {/* Ícono de la pestaña */}
            <span className="flex-shrink-0">
              {tab.icon}
            </span>

            {/* Etiqueta de la pestaña */}
            <span className="whitespace-nowrap">
              {tab.label}
            </span>

            {/* Indicador visual para pestaña activa */}
            {activeTab === tab.id && (
              <div className={`
                absolute bottom-0 left-0 right-0 h-0.5 rounded-t-full
                ${isDark ? 'bg-blue-400' : 'bg-blue-600'}
                animate-in slide-in duration-200
              `} />
            )}
          </button>
        ))}
      </div>

      {/* Área flexible para elementos adicionales */}
      <div className="flex-1" />

      {/* Indicador de navegación (opcional) */}
      <div className={`
        flex items-center px-4 text-xs
        ${isDark ? 'text-gray-500' : 'text-gray-400'}
      `}>
        <ChevronRight size={12} className="mr-1" />
        <span className="capitalize">{activeTab}</span>
      </div>
    </div>
  );
};

// Hook personalizado para gestionar el estado de las pestañas
export const useTabNavigation = (initialTab: string = 'inicio') => {
  const [currentTab, setCurrentTab] = useState(initialTab);

  const navigateToTab = (tabId: string) => {
    setCurrentTab(tabId);
  };

  const isTabActive = (tabId: string) => currentTab === tabId;

  return {
    currentTab,
    navigateToTab,
    isTabActive,
  };
};

export default HorizontalTabBar;
