/**
 * Sistema de notificaciones nativas para Tauri
 * Utiliza las APIs nativas del sistema operativo para mostrar notificaciones
 */

import { isPermissionGranted, requestPermission, sendNotification } from '@tauri-apps/api/notification';
import type { NotificationType } from '../types/notifications';

export interface NativeNotificationOptions {
  title: string;
  body: string;
  icon?: string;
  sound?: string;
  priority?: 'low' | 'normal' | 'high' | 'critical';
}

export interface NotificationConfig {
  enableNative: boolean;
  enableToast: boolean;
  criticalOnly: boolean;
  soundEnabled: boolean;
}

// Configuración por defecto
const DEFAULT_CONFIG: NotificationConfig = {
  enableNative: true,
  enableToast: true,
  criticalOnly: false,
  soundEnabled: false,
};

// Estado global de configuración
let notificationConfig: NotificationConfig = { ...DEFAULT_CONFIG };

/**
 * Inicializa el sistema de notificaciones nativas
 */
export const initializeNativeNotifications = async (): Promise<boolean> => {
  try {
    // Verificar si los permisos ya están concedidos
    let permissionGranted = await isPermissionGranted();
    
    // Si no están concedidos, solicitarlos
    if (!permissionGranted) {
      const permission = await requestPermission();
      permissionGranted = permission === 'granted';
    }
    
    if (permissionGranted) {
      console.log('Permisos de notificación concedidos');
      return true;
    } else {
      console.warn('Permisos de notificación denegados');
      return false;
    }
  } catch (error) {
    console.error('Error al inicializar notificaciones nativas:', error);
    return false;
  }
};

/**
 * Actualiza la configuración de notificaciones
 */
export const updateNotificationConfig = (config: Partial<NotificationConfig>): void => {
  notificationConfig = { ...notificationConfig, ...config };
};

/**
 * Obtiene la configuración actual de notificaciones
 */
export const getNotificationConfig = (): NotificationConfig => {
  return { ...notificationConfig };
};

/**
 * Determina si una notificación debe mostrarse como nativa
 */
const shouldShowNativeNotification = (type: NotificationType, priority: 'low' | 'normal' | 'high' | 'critical' = 'normal'): boolean => {
  if (!notificationConfig.enableNative) return false;
  
  // Si está configurado para solo críticas, verificar prioridad
  if (notificationConfig.criticalOnly) {
    return priority === 'critical' || type === 'error';
  }
  
  // Mostrar nativas para operaciones importantes
  return type === 'error' || type === 'warning' || priority === 'high' || priority === 'critical';
};

/**
 * Envía una notificación nativa del sistema operativo
 */
export const sendNativeNotification = async (
  type: NotificationType,
  options: NativeNotificationOptions
): Promise<boolean> => {
  try {
    const priority = options.priority || 'normal';
    
    // Verificar si debe mostrarse como notificación nativa
    if (!shouldShowNativeNotification(type, priority)) {
      return false;
    }
    
    // Verificar permisos
    const permissionGranted = await isPermissionGranted();
    if (!permissionGranted) {
      console.warn('No hay permisos para mostrar notificaciones nativas');
      return false;
    }
    
    // Configurar el ícono según el tipo
    const iconPath = options.icon || getDefaultIcon(type);
    
    // Enviar la notificación
    await sendNotification({
      title: options.title,
      body: options.body,
      icon: iconPath,
      sound: notificationConfig.soundEnabled ? options.sound : undefined,
    });
    
    console.log(`Notificación nativa enviada: ${options.title}`);
    return true;
  } catch (error) {
    console.error('Error al enviar notificación nativa:', error);
    return false;
  }
};

/**
 * Obtiene el ícono por defecto según el tipo de notificación
 */
const getDefaultIcon = (type: NotificationType): string => {
  // En Tauri, podemos usar iconos del sistema o iconos personalizados
  switch (type) {
    case 'success':
      return '/icons/success.png';
    case 'warning':
      return '/icons/warning.png';
    case 'error':
      return '/icons/error.png';
    case 'info':
    default:
      return '/pipe_logo.svg';
  }
};

/**
 * Notificaciones predefinidas para operaciones comunes
 */
export const showOperationCompleteNotification = async (operation: string): Promise<boolean> => {
  return await sendNativeNotification('success', {
    title: 'Operación Completada',
    body: `${operation} se ha completado exitosamente`,
    priority: 'normal',
  });
};

export const showOperationErrorNotification = async (operation: string, error: string): Promise<boolean> => {
  return await sendNativeNotification('error', {
    title: 'Error en Operación',
    body: `Error en ${operation}: ${error}`,
    priority: 'high',
  });
};

export const showSystemWarningNotification = async (message: string): Promise<boolean> => {
  return await sendNativeNotification('warning', {
    title: 'Advertencia del Sistema',
    body: message,
    priority: 'high',
  });
};

export const showCriticalErrorNotification = async (message: string): Promise<boolean> => {
  return await sendNativeNotification('error', {
    title: 'Error Crítico - Hydra21',
    body: message,
    priority: 'critical',
  });
};

/**
 * Notificaciones específicas para Hydra21
 */
export const showCalculationCompleteNotification = async (calculationType: string): Promise<boolean> => {
  return await sendNativeNotification('success', {
    title: 'Cálculo Hidráulico Completado',
    body: `El cálculo de ${calculationType} ha finalizado correctamente`,
    priority: 'normal',
  });
};

export const showProjectSavedNotification = async (projectName: string): Promise<boolean> => {
  return await sendNativeNotification('success', {
    title: 'Proyecto Guardado',
    body: `El proyecto "${projectName}" se ha guardado correctamente`,
    priority: 'normal',
  });
};

export const showExportCompleteNotification = async (format: string, filename: string): Promise<boolean> => {
  return await sendNativeNotification('success', {
    title: 'Exportación Completada',
    body: `Archivo ${format} exportado como "${filename}"`,
    priority: 'normal',
  });
};

export const showAIAnalysisCompleteNotification = async (): Promise<boolean> => {
  return await sendNativeNotification('info', {
    title: 'Análisis de IA Completado',
    body: 'El análisis inteligente de tu proyecto ha finalizado. Revisa los resultados.',
    priority: 'normal',
  });
};

/**
 * Función de utilidad para decidir entre notificación nativa y toast
 */
export const shouldUseToastNotification = (type: NotificationType, priority: 'low' | 'normal' | 'high' | 'critical' = 'normal'): boolean => {
  // Usar toast para notificaciones ligeras y feedback inmediato
  if (!notificationConfig.enableToast) return false;
  
  // Siempre usar toast para info de baja prioridad
  if (type === 'info' && priority === 'low') return true;
  
  // Usar toast para feedback de UI inmediato
  if (priority === 'low' || priority === 'normal') return true;
  
  // Para prioridades altas, usar ambos si están habilitados
  return notificationConfig.enableToast;
};

export default {
  initialize: initializeNativeNotifications,
  send: sendNativeNotification,
  updateConfig: updateNotificationConfig,
  getConfig: getNotificationConfig,
  shouldUseToast: shouldUseToastNotification,
  // Notificaciones predefinidas
  operationComplete: showOperationCompleteNotification,
  operationError: showOperationErrorNotification,
  systemWarning: showSystemWarningNotification,
  criticalError: showCriticalErrorNotification,
  calculationComplete: showCalculationCompleteNotification,
  projectSaved: showProjectSavedNotification,
  exportComplete: showExportCompleteNotification,
  aiAnalysisComplete: showAIAnalysisCompleteNotification,
};
