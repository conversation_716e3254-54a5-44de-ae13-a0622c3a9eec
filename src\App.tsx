/**
 * Componente principal de la aplicación Hydra21
 * Integra la titlebar personalizada y el contenido principal
 */

import { useState } from 'react';
import { CustomTitlebar } from './components/ui/CustomTitlebar';
import { HorizontalTabBar, useTabNavigation } from './components/ui/HorizontalTabBar';
import { TabContent } from './components/ui/TabContent';
import { ToastContainer } from './components/ui/Toast';
import { useTheme } from './hooks/useTheme';
import { useNotifications, useToast } from './hooks/useNotifications';
import reactLogo from './assets/react.svg';
import viteLogo from '/vite.svg';

function App() {
  const [count, setCount] = useState(0);
  const { isDark, toggleTheme, os } = useTheme();
  const { toasts, hideToast } = useNotifications();
  const toast = useToast();

  // Hook para gestionar la navegación entre pestañas
  const { currentTab, navigateToTab } = useTabNavigation('inicio');

  // Manejador para elementos del menú
  const handleMenuItemClick = (menuId: string, itemId: string) => {
    console.log(`Menu action: ${menuId} -> ${itemId}`);

    // Implementar acciones específicas según el elemento del menú
    switch (`${menuId}.${itemId}`) {
      case 'view.toggle-theme':
        toggleTheme();
        toast.success('Tema cambiado', `Cambiado a modo ${isDark ? 'claro' : 'oscuro'}`);
        break;
      case 'file.exit':
        toast.warning('Cerrando aplicación', 'La aplicación se cerrará en unos momentos');
        break;
      default:
        console.log(`Acción no implementada: ${menuId}.${itemId}`);
    }
  };

  // Manejador para cambio de pestañas
  const handleTabChange = (tabId: string) => {
    navigateToTab(tabId);
    toast.info('Navegación', `Cambiando a la pestaña: ${tabId.charAt(0).toUpperCase() + tabId.slice(1)}`);
  };

  // Funciones de demostración para notificaciones
  const showDemoNotifications = () => {
    toast.info('Información', 'Esta es una notificación de información');
    setTimeout(() => {
      toast.success('Éxito', 'Operación completada correctamente');
    }, 1000);
    setTimeout(() => {
      toast.warning('Advertencia', 'Esto es una advertencia importante');
    }, 2000);
    setTimeout(() => {
      toast.error('Error', 'Se ha producido un error en el sistema');
    }, 3000);
  };

  return (
    <div className="flex flex-col h-screen overflow-hidden">
      {/* Titlebar personalizada con funcionalidades avanzadas */}
      <CustomTitlebar
        title="Hydra21 App"
        showIcon={true}
        showMenuBar={true}
        enableDoubleClickMaximize={true}
        enableKeyboardShortcuts={true}
        onMenuItemClick={handleMenuItemClick}
      />

      {/* Barra de navegación horizontal */}
      <HorizontalTabBar
        onTabChange={handleTabChange}
        defaultTab={currentTab}
        className="h-12"
      />

      {/* Contenido principal */}
      <main className="flex-1 overflow-auto">
        <div className="min-h-full bg-gradient-to-br from-background to-secondary/20">
          {/* Contenido de la pestaña activa */}
          <TabContent tabId={currentTab} />

          {/* Contenido de demostración adicional - Solo visible en pestaña Inicio */}
          {currentTab === 'inicio' && (
            <div className="container mx-auto px-6 py-4">
              {/* Header con información del sistema */}
              <div className="mb-6 text-center">
                <div className="mt-2 flex items-center justify-center gap-4 text-sm text-muted-foreground">
                  <span>Sistema: {os.charAt(0).toUpperCase() + os.slice(1)}</span>
                  <span>•</span>
                  <span>Tema: {isDark ? 'Oscuro' : 'Claro'}</span>
                  <span>•</span>
                  <span>Pestaña: {currentTab.charAt(0).toUpperCase() + currentTab.slice(1)}</span>
                </div>
              </div>

              {/* Sección de demostración compacta */}
              <div className="max-w-3xl mx-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  {/* Card de tecnologías */}
                  <div className={`
                    rounded-lg p-6 transition-all duration-300
                    ${isDark ? 'glass-card-dark' : 'glass-card'}
                  `}>
                  <h2 className="text-xl font-semibold mb-4">Tecnologías</h2>
                  <div className="flex items-center justify-center gap-8 mb-4">
                    <a href="https://vitejs.dev" target="_blank" rel="noopener noreferrer">
                      <img src={viteLogo} className="h-16 w-16 hover:scale-110 transition-transform" alt="Vite logo" />
                    </a>
                    <a href="https://react.dev" target="_blank" rel="noopener noreferrer">
                      <img src={reactLogo} className="h-16 w-16 hover:scale-110 transition-transform animate-spin-slow" alt="React logo" />
                    </a>
                  </div>
                  <p className="text-sm text-muted-foreground text-center">
                    Vite + React + TypeScript + TailwindCSS
                  </p>
                  </div>

                  {/* Card de controles */}
                  <div className={`
                    rounded-lg p-6 transition-all duration-300
                    ${isDark ? 'glass-card-dark' : 'glass-card'}
                  `}>
                  <h2 className="text-xl font-semibold mb-4">Controles</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Contador:</span>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setCount(count - 1)}
                          className="px-3 py-1 bg-secondary hover:bg-secondary/80 rounded transition-colors"
                        >
                          -
                        </button>
                        <span className="font-mono text-lg min-w-[3rem] text-center">{count}</span>
                        <button
                          onClick={() => setCount(count + 1)}
                          className="px-3 py-1 bg-secondary hover:bg-secondary/80 rounded transition-colors"
                        >
                          +
                        </button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span>Tema:</span>
                      <button
                        onClick={toggleTheme}
                        className="px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 rounded transition-colors"
                      >
                        Cambiar a {isDark ? 'Claro' : 'Oscuro'}
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <span>Notificaciones:</span>
                      <button
                        onClick={showDemoNotifications}
                        className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded transition-colors"
                      >
                        Mostrar Demo
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Información adicional */}
              <div className={`
                rounded-lg p-6 transition-all duration-300
                ${isDark ? 'glass-card-dark' : 'glass-card'}
              `}>
                <h2 className="text-xl font-semibold mb-4">Características Implementadas</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <h3 className="font-medium mb-2 text-blue-600 dark:text-blue-400">Core Features</h3>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Titlebar personalizada nativa
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Controles de ventana por OS
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Tema claro/oscuro adaptable
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Detección de sistema operativo
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Ventana sin decoraciones nativas
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2 text-purple-600 dark:text-purple-400">Enhanced Features</h3>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Barra de menú integrada
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Ícono de app clickeable
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Menú contextual del sistema
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Doble clic para maximizar
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Atajos de teclado (Alt+Space)
                      </li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2 text-orange-600 dark:text-orange-400">UX Features</h3>
                    <ul className="space-y-2">
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Arrastre de ventana funcional
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Animaciones suaves
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Tipografía del sistema
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Indicadores de estado
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        Soporte de accesibilidad
                      </li>
                    </ul>
                  </div>
                </div>

                <div className={`
                  mt-6 p-4 rounded-lg transition-all duration-300
                  ${isDark ? 'glass-effect-dark' : 'glass-effect'}
                `}>
                  <h3 className="font-medium mb-2 text-blue-800 dark:text-blue-200">Instrucciones de Uso</h3>
                  <ul className="text-sm space-y-1 text-blue-700 dark:text-blue-300">
                    <li>• <strong>Clic en el ícono "H"</strong>: Abre el menú del sistema</li>
                    <li>• <strong>Doble clic en la titlebar</strong>: Maximiza/restaura la ventana</li>
                    <li>• <strong>Alt + Space</strong>: Abre el menú del sistema (Windows)</li>
                    <li>• <strong>Menús File/Edit/View/Help</strong>: Funcionalidades estándar de aplicación</li>
                    <li>• <strong>Arrastrar titlebar</strong>: Mueve la ventana</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Contenedor de notificaciones toast */}
      <ToastContainer
        toasts={toasts}
        onClose={hideToast}
        position="top-right"
      />
    </div>
  );
}

export default App;
