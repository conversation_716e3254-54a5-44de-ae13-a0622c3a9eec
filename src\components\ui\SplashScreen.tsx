import React, { useEffect, useState, useCallback } from 'react';

interface SplashScreenProps {
  onComplete: () => void;
  minDuration?: number; // Duración mínima en ms
  isDark?: boolean; // Tema opcional
}

// Hook simple para detectar preferencia de tema
const useThemeDetection = (defaultDark: boolean = true) => {
  const [isDark, setIsDark] = useState(() => {
    if (typeof window === 'undefined') return defaultDark;
    
    // Detectar preferencia del sistema
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    return mediaQuery.matches ?? defaultDark;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => setIsDark(e.matches);
    
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return { isDark };
};

export const SplashScreen: React.FC<SplashScreenProps> = ({
  onComplete,
  minDuration = 2000,
  isDark: providedTheme
}) => {
  const { isDark: detectedTheme } = useThemeDetection();
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [loadingText, setLoadingText] = useState('Iniciando...');

  // Textos de carga dinámicos
  const loadingTexts = [
    'Iniciando...',
    'Cargando módulos...',
    'Configurando interfaz...',
    'Preparando herramientas...',
    'Finalizando...'
  ];

  const handleComplete = useCallback(() => {
    setIsVisible(false);
    setTimeout(() => {
      onComplete();
    }, 500); // Tiempo para la animación de salida
  }, [onComplete]);

  useEffect(() => {
    // Simular progreso de carga
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        const newProgress = prev + Math.random() * 15 + 5;
        
        // Cambiar texto según el progreso
        const textIndex = Math.floor((newProgress / 100) * (loadingTexts.length - 1));
        setLoadingText(loadingTexts[textIndex]);
        
        return newProgress;
      });
    }, 100);

    // Timer mínimo para mostrar el splash
    const minTimer = setTimeout(() => {
      if (progress >= 100) {
        handleComplete();
      }
    }, minDuration);

    return () => {
      clearInterval(progressInterval);
      clearTimeout(minTimer);
    };
  }, [minDuration, progress, handleComplete, loadingTexts]);

  useEffect(() => {
    if (progress >= 100) {
      setLoadingText('¡Listo!');
      // Pequeño delay antes de completar para suavizar la transición
      const completeTimer = setTimeout(() => {
        handleComplete();
      }, 300);

      return () => clearTimeout(completeTimer);
    }
  }, [progress, handleComplete]);

  if (!isVisible) {
    return (
      <div className="raycast-splash-exit" />
    );
  }

  return (
    <div className="raycast-splash-container">
      {/* Efectos de fondo animados */}
      <div className="raycast-splash-background">
        {/* Partículas flotantes */}
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="raycast-splash-particle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}

        {/* Círculos de fondo mejorados */}
        <div className="raycast-splash-circle raycast-splash-circle-1" />
        <div className="raycast-splash-circle raycast-splash-circle-2" />
        <div className="raycast-splash-circle raycast-splash-circle-3" />
        
        {/* Grid de fondo sutil */}
        <div className="raycast-splash-grid" />
      </div>

      {/* Contenido principal */}
      <div className="raycast-splash-content">
        {/* Logo mejorado */}
        <div className="raycast-splash-logo-container">
          <div className="raycast-splash-logo">
            <div className="raycast-splash-logo-inner">
              <img
                src="/pipe_logo.svg"
                alt="Hydra21"
                className="raycast-splash-logo-image"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  target.parentElement!.innerHTML = '<span class="raycast-splash-logo-fallback">H21</span>';
                }}
              />
            </div>
            {/* Anillo de progreso alrededor del logo */}
            <svg className="raycast-splash-logo-ring" viewBox="0 0 100 100">
              <circle
                cx="50"
                cy="50"
                r="45"
                className="raycast-splash-logo-ring-bg"
              />
              <circle
                cx="50"
                cy="50"
                r="45"
                className="raycast-splash-logo-ring-progress"
                style={{
                  strokeDasharray: `${2 * Math.PI * 45}`,
                  strokeDashoffset: `${2 * Math.PI * 45 * (1 - progress / 100)}`
                }}
              />
            </svg>
          </div>
        </div>

        {/* Título mejorado */}
        <div className="raycast-splash-title-container">
          <h1 className="raycast-splash-title">
            <span className="raycast-splash-title-text">Hydra21</span>
            <div className="raycast-splash-title-underline" />
          </h1>
          <p className="raycast-splash-subtitle">
            Plataforma de Ingeniería Hidráulica
          </p>
        </div>

        {/* Barra de progreso mejorada */}
        <div className="raycast-splash-progress-container">
          <div className="raycast-splash-progress-bar">
            <div 
              className="raycast-splash-progress-fill"
              style={{ width: `${Math.min(progress, 100)}%` }}
            >
              <div className="raycast-splash-progress-shine" />
            </div>
          </div>
          
          {/* Información de progreso */}
          <div className="raycast-splash-progress-info">
            <span className="raycast-splash-progress-text">{loadingText}</span>
            <span className="raycast-splash-progress-percent">
              {Math.round(Math.min(progress, 100))}%
            </span>
          </div>
        </div>

        {/* Indicadores de carga mejorados */}
        <div className="raycast-splash-dots">
          {[0, 1, 2, 3].map((i) => (
            <div
              key={i}
              className="raycast-splash-dot"
              style={{
                animationDelay: `${i * 0.15}s`
              }}
            />
          ))}
        </div>
      </div>

      <style>{`
        /* Variables específicas para el splash */
        .raycast-splash-container {
          --splash-accent: #ff6363;
          --splash-accent-secondary: #63a7ff;
          --splash-glow: rgba(255, 99, 99, 0.3);
          --splash-particle-size: 3px;
        }

        /* Contenedor principal */
        .raycast-splash-container {
          position: fixed;
          inset: 0;
          z-index: 9999;
          display: flex;
          align-items: center;
          justify-content: center;
          background: var(--raycast-bg-primary);
          overflow: hidden;
          animation: raycast-splash-fade-in 0.5s ease;
        }

        /* Salida animada */
        .raycast-splash-exit {
          position: fixed;
          inset: 0;
          z-index: 9999;
          background: var(--raycast-bg-primary);
          opacity: 0;
          transition: opacity 0.5s ease;
        }

        /* Efectos de fondo */
        .raycast-splash-background {
          position: absolute;
          inset: 0;
          overflow: hidden;
        }

        /* Partículas flotantes */
        .raycast-splash-particle {
          position: absolute;
          width: var(--splash-particle-size);
          height: var(--splash-particle-size);
          background: var(--splash-accent);
          border-radius: 50%;
          opacity: 0.6;
          animation: raycast-splash-float infinite ease-in-out;
        }

        /* Círculos de fondo */
        .raycast-splash-circle {
          position: absolute;
          border-radius: 50%;
          background: radial-gradient(circle, var(--splash-glow) 0%, transparent 70%);
          animation: raycast-splash-pulse 4s infinite ease-in-out;
        }

        .raycast-splash-circle-1 {
          width: 300px;
          height: 300px;
          top: 10%;
          left: -10%;
          animation-delay: 0s;
        }

        .raycast-splash-circle-2 {
          width: 200px;
          height: 200px;
          bottom: 20%;
          right: -5%;
          animation-delay: 1.5s;
        }

        .raycast-splash-circle-3 {
          width: 150px;
          height: 150px;
          top: 60%;
          left: 70%;
          animation-delay: 3s;
        }

        /* Grid de fondo */
        .raycast-splash-grid {
          position: absolute;
          inset: 0;
          background-image: 
            linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
          background-size: 50px 50px;
          opacity: 0.5;
          animation: raycast-splash-grid-move 20s linear infinite;
        }

        /* Contenido principal */
        .raycast-splash-content {
          position: relative;
          z-index: 10;
          text-align: center;
          padding: var(--raycast-space-8);
          background: var(--raycast-surface-primary);
          backdrop-filter: var(--raycast-blur-md);
          border: 1px solid var(--raycast-border-primary);
          border-radius: var(--raycast-radius-2xl);
          box-shadow: var(--raycast-shadow-xl);
          max-width: 400px;
          width: 90%;
          animation: raycast-splash-content-in 0.8s ease 0.2s both;
        }

        /* Logo mejorado */
        .raycast-splash-logo-container {
          margin-bottom: var(--raycast-space-8);
        }

        .raycast-splash-logo {
          position: relative;
          width: 120px;
          height: 120px;
          margin: 0 auto;
        }

        .raycast-splash-logo-inner {
          width: 80px;
          height: 80px;
          margin: 20px auto;
          background: linear-gradient(135deg, var(--splash-accent), var(--splash-accent-secondary));
          border-radius: var(--raycast-radius-xl);
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 
            0 0 30px var(--splash-glow),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
          animation: raycast-splash-logo-glow 2s ease-in-out infinite alternate;
        }

        .raycast-splash-logo-image {
          width: 40px;
          height: 40px;
          filter: brightness(0) invert(1);
        }

        .raycast-splash-logo-fallback {
          font-size: 24px;
          font-weight: 700;
          color: white;
        }

        /* Anillo de progreso del logo */
        .raycast-splash-logo-ring {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          transform: rotate(-90deg);
        }

        .raycast-splash-logo-ring-bg {
          fill: none;
          stroke: var(--raycast-border-primary);
          stroke-width: 2;
        }

        .raycast-splash-logo-ring-progress {
          fill: none;
          stroke: var(--splash-accent);
          stroke-width: 3;
          stroke-linecap: round;
          transition: stroke-dashoffset 0.3s ease;
          filter: drop-shadow(0 0 6px var(--splash-glow));
        }

        /* Título mejorado */
        .raycast-splash-title-container {
          margin-bottom: var(--raycast-space-8);
        }

        .raycast-splash-title {
          position: relative;
          margin-bottom: var(--raycast-space-3);
        }

        .raycast-splash-title-text {
          font-size: var(--raycast-text-4xl);
          font-weight: 700;
          background: linear-gradient(135deg, var(--raycast-text-primary), var(--splash-accent));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          letter-spacing: -0.02em;
        }

        .raycast-splash-title-underline {
          height: 3px;
          background: linear-gradient(90deg, transparent, var(--splash-accent), transparent);
          border-radius: 2px;
          margin: var(--raycast-space-2) auto 0;
          max-width: 100px;
          animation: raycast-splash-underline-expand 1s ease 0.5s both;
        }

        .raycast-splash-subtitle {
          color: var(--raycast-text-secondary);
          font-size: var(--raycast-text-sm);
          font-weight: 500;
          letter-spacing: 0.5px;
        }

        /* Barra de progreso mejorada */
        .raycast-splash-progress-container {
          margin-bottom: var(--raycast-space-6);
        }

        .raycast-splash-progress-bar {
          height: 6px;
          background: var(--raycast-bg-tertiary);
          border-radius: 3px;
          overflow: hidden;
          margin-bottom: var(--raycast-space-3);
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .raycast-splash-progress-fill {
          height: 100%;
          background: linear-gradient(90deg, var(--splash-accent), var(--splash-accent-secondary));
          border-radius: 3px;
          transition: width 0.3s ease;
          position: relative;
          overflow: hidden;
          box-shadow: 0 0 10px var(--splash-glow);
        }

        .raycast-splash-progress-shine {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
          animation: raycast-splash-shine 2s infinite;
        }

        .raycast-splash-progress-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .raycast-splash-progress-text {
          color: var(--raycast-text-secondary);
          font-size: var(--raycast-text-sm);
          font-weight: 500;
        }

        .raycast-splash-progress-percent {
          color: var(--splash-accent);
          font-size: var(--raycast-text-sm);
          font-weight: 600;
          font-family: var(--raycast-font-mono);
        }

        /* Indicadores de carga */
        .raycast-splash-dots {
          display: flex;
          justify-content: center;
          gap: var(--raycast-space-2);
        }

        .raycast-splash-dot {
          width: 8px;
          height: 8px;
          background: var(--splash-accent);
          border-radius: 50%;
          animation: raycast-splash-dot-bounce 1.2s infinite ease-in-out;
          box-shadow: 0 0 6px var(--splash-glow);
        }

        /* Animaciones */
        @keyframes raycast-splash-fade-in {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        @keyframes raycast-splash-content-in {
          from {
            opacity: 0;
            transform: translateY(30px) scale(0.9);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @keyframes raycast-splash-float {
          0%, 100% {
            transform: translateY(0) scale(1);
            opacity: 0.6;
          }
          50% {
            transform: translateY(-20px) scale(1.1);
            opacity: 1;
          }
        }

        @keyframes raycast-splash-pulse {
          0%, 100% {
            transform: scale(1);
            opacity: 0.3;
          }
          50% {
            transform: scale(1.1);
            opacity: 0.6;
          }
        }

        @keyframes raycast-splash-grid-move {
          0% {
            transform: translate(0, 0);
          }
          100% {
            transform: translate(50px, 50px);
          }
        }

        @keyframes raycast-splash-logo-glow {
          0% {
            box-shadow: 
              0 0 20px var(--splash-glow),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
          }
          100% {
            box-shadow: 
              0 0 40px var(--splash-glow),
              0 0 60px rgba(255, 99, 99, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
          }
        }

        @keyframes raycast-splash-underline-expand {
          from {
            width: 0;
          }
          to {
            width: 100px;
          }
        }

        @keyframes raycast-splash-shine {
          0% {
            left: -100%;
          }
          100% {
            left: 100%;
          }
        }

        @keyframes raycast-splash-dot-bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0) scale(1);
          }
          40% {
            transform: translateY(-8px) scale(1.1);
          }
          60% {
            transform: translateY(-4px) scale(1.05);
          }
        }

        /* Responsive */
        @media (max-width: 480px) {
          .raycast-splash-content {
            margin: var(--raycast-space-4);
            padding: var(--raycast-space-6);
          }

          .raycast-splash-logo {
            width: 100px;
            height: 100px;
          }

          .raycast-splash-logo-inner {
            width: 70px;
            height: 70px;
            margin: 15px auto;
          }

          .raycast-splash-title-text {
            font-size: var(--raycast-text-3xl);
          }
        }
      `}</style>
    </div>
  );
};

export default SplashScreen;