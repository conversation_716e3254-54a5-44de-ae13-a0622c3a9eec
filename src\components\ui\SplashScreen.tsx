/**
 * Componente SplashScreen
 * Pantalla de carga moderna con diseño glassmorphism para Hydra21
 */

import React, { useEffect, useState, useCallback } from 'react';
import { useTheme } from '../../hooks/useTheme';

interface SplashScreenProps {
  onComplete: () => void;
  minDuration?: number; // Duración mínima en ms
}

export const SplashScreen: React.FC<SplashScreenProps> = ({
  onComplete,
  minDuration = 2000
}) => {
  const { isDark } = useTheme();
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const handleComplete = useCallback(() => {
    setIsVisible(false);
    setTimeout(() => {
      onComplete();
    }, 500); // Tiempo para la animación de salida
  }, [onComplete]);

  useEffect(() => {
    // Simular progreso de carga
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval);
          return 100;
        }
        return prev + Math.random() * 15 + 5; // Incremento aleatorio entre 5-20
      });
    }, 100);

    // Timer mínimo para mostrar el splash
    const minTimer = setTimeout(() => {
      if (progress >= 100) {
        handleComplete();
      }
    }, minDuration);

    return () => {
      clearInterval(progressInterval);
      clearTimeout(minTimer);
    };
  }, [minDuration, progress, handleComplete]);

  useEffect(() => {
    if (progress >= 100) {
      // Pequeño delay antes de completar para suavizar la transición
      const completeTimer = setTimeout(() => {
        handleComplete();
      }, 300);

      return () => clearTimeout(completeTimer);
    }
  }, [progress, handleComplete]);

  if (!isVisible) {
    return (
      <div className={`
        fixed inset-0 z-50 flex items-center justify-center
        transition-opacity duration-500 opacity-0
        ${isDark ? 'bg-gray-900' : 'bg-white'}
      `} />
    );
  }

  return (
    <div className={`
      fixed inset-0 z-50 flex items-center justify-center
      transition-opacity duration-300
      ${isDark
        ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900'
        : 'bg-gradient-to-br from-blue-50 via-white to-blue-50'
      }
    `}>
      {/* Efectos de fondo */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Círculos decorativos con glassmorphism */}
        <div className={`
          absolute top-1/4 left-1/4 w-64 h-64 rounded-full
          ${isDark ? 'glass-effect-dark' : 'glass-effect'}
          animate-pulse opacity-20
        `} />
        <div className={`
          absolute bottom-1/4 right-1/4 w-48 h-48 rounded-full
          ${isDark ? 'glass-effect-dark' : 'glass-effect'}
          animate-pulse opacity-30
        `} style={{ animationDelay: '1s' }} />
        <div className={`
          absolute top-1/2 right-1/3 w-32 h-32 rounded-full
          ${isDark ? 'glass-effect-dark' : 'glass-effect'}
          animate-pulse opacity-25
        `} style={{ animationDelay: '0.5s' }} />
      </div>

      {/* Contenido principal */}
      <div className={`
        relative z-10 text-center p-8 rounded-2xl
        ${isDark ? 'glass-card-dark' : 'glass-card'}
        max-w-md w-full mx-4
        animate-in fade-in slide-in duration-700
      `}>
        {/* Logo */}
        <div className="mb-8">
          <div className={`
            w-24 h-24 mx-auto rounded-2xl flex items-center justify-center
            ${isDark
              ? 'bg-gradient-to-br from-blue-500 to-purple-600'
              : 'bg-gradient-to-br from-blue-600 to-purple-700'
            }
            shadow-2xl animate-pulse
          `}>
            <img
              src="/pipe_logo.svg"
              alt="Hydra21"
              className="w-12 h-12 text-white"
              onError={(e) => {
                // Fallback si no se encuentra el logo
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.parentElement!.innerHTML = '<span class="text-2xl font-bold text-white">H21</span>';
              }}
            />
          </div>
        </div>

        {/* Título y descripción */}
        <div className="mb-8">
          <h1 className={`
            text-3xl font-bold mb-2
            ${isDark
              ? 'bg-gradient-to-r from-blue-400 to-purple-400'
              : 'bg-gradient-to-r from-blue-600 to-purple-600'
            }
            bg-clip-text text-transparent
          `}>
            Hydra21
          </h1>
          <p className={`
            text-sm
            ${isDark ? 'text-gray-300' : 'text-gray-600'}
          `}>
            Plataforma de Ingeniería Hidráulica
          </p>
        </div>

        {/* Barra de progreso */}
        <div className="mb-6">
          <div className={`
            w-full h-2 rounded-full overflow-hidden
            ${isDark ? 'bg-gray-700' : 'bg-gray-200'}
          `}>
            <div
              className={`
                h-full rounded-full transition-all duration-300 ease-out
                ${isDark
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600'
                }
              `}
              style={{ width: `${Math.min(progress, 100)}%` }}
            />
          </div>
          <div className={`
            mt-2 text-xs
            ${isDark ? 'text-gray-400' : 'text-gray-500'}
          `}>
            {Math.round(Math.min(progress, 100))}% Cargando...
          </div>
        </div>

        {/* Indicador de carga animado */}
        <div className="flex justify-center space-x-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={`
                w-2 h-2 rounded-full
                ${isDark ? 'bg-blue-400' : 'bg-blue-600'}
                animate-pulse
              `}
              style={{
                animationDelay: `${i * 0.2}s`,
                animationDuration: '1s'
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;
